import MarkdownRenderer from '@/components/MarkdownRenderer'
import ScoreRadarChart from '@/components/ScoreRadarChart'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { useReviewArticle } from '@/hooks/useReviewArticle'
import { snapdom } from '@zumer/snapdom'
import React, { useRef, useState } from 'react'



// 示例文章
const sampleEssay = `那一刻，我豁然开朗

这件事还要从刚刚进入初中的那一个月说起，新组建的班级需要招募几个"临时工"班委来管理这一个月的各项事务，在这一个月过去后会从工作积极认真的"临时工"中筛选一批同学，竞选今后一年的班委。那一刻，我为班级服务的一腔热血仿佛被点燃了，立刻积极地报了名，迫切地想为班级共献自己的一份力量。同时，能当个"一官半职"，岂不更好？

幸福来得太突然，我以这个班级的代理班长身份进行班级展示视频的拍摄与制作，其实我并不知道班长的本职工作是什么，只是把心成浸在视频的拍摄中。从此校园里多了这样的一些场景：晨曦校园一角，我们几个拍摄小草身上晶莹的露珠；午后教室窗台上陪伴我们一起听课的蝴蝶成了我们视频里的常客；傍晚时分球场旁香樟树下的落日余晖都成为了我摄像机中的客人——

一个月的时间转瞬即逝，我代理班长的身份也即将迎来蜕变，学校要求我们每个人准备一个5分钟的演讲，之后让班级同学投票，票数多者胜出，

我陷入了焦虑与迷惘，事实上我根本不会演讲，也不知道该说什么，我经常在准备演讲时走神，晨曦校园一角小草身上没有了晶莹的露珠，午后教室窗台上陪伴我们一起听课的蝴蝶飞走了，傍晚时分球场旁香樟树下的落日余晖也迎来了不眠的黑夜。面对着一个无助而恐惧的自己，在睡梦中也能感受到自己即将被波夺代理班长职位的绝望与迷惘，

班主任似乎注意到了我的变化，在谈话中，我说出了我的心声，班主任则耐心地启发我，在当代理班长时，感觉最大的收获是什么？我沉思良久，回答说:"是为同学们做事的满足与乐趣"。班主任说？"这就是价值，你应该勇敢面对的是如何持续输出自己的价值，班长不班长并不重要，唯有卸下承重的取壳才能让精神自由飞翔！"

那一刻，老师的话我豁然开朗，只要持续输出你的价值，这个世界回报你的不止于名分！诚斯然也，惟有卸下包袱，才能轻装上阵，唯有淡化名利！才能在呈现价值的成长历程中勇敢面对自己，以期未来遇见更好的自己！`

// 评分步骤定义
const SCORING_STEPS = [
  { key: 'word_count', label: '字数统计', icon: '📊', description: '统计文章字数并进行基础检查' },
  { key: 'topic_analysis', label: '主题立意', icon: '🎯', description: '分析文章主题的明确性和深度' },
  { key: 'structure_analysis', label: '结构层次', icon: '🏗️', description: '评估文章的结构组织和逻辑' },
  { key: 'content_analysis', label: '内容表达', icon: '📝', description: '分析内容的丰富性和表达效果' },
  { key: 'language_analysis', label: '语言文字', icon: '✍️', description: '评价语言运用和文字表达' },
  { key: 'final_scoring', label: '综合评分', icon: '🎖️', description: '计算最终得分和生成总结' },
  { key: 'summary', label: '评分完成', icon: '✅', description: '生成详细评分报告' }
]

const getScoreColor = (score: number, maxScore: number) => {
  const percentage = (score / maxScore) * 100
  if (percentage >= 90) return 'success'
  if (percentage >= 80) return 'info'
  if (percentage >= 70) return 'warning'
  if (percentage >= 60) return 'secondary'
  return 'destructive'
}

const getStepStatus = (stepKey: string, currentStep: string, stepResults: Record<string, any>, isLoading: boolean) => {
  if (stepResults[stepKey]) return 'completed'
  if (currentStep.includes(stepKey) || currentStep.includes(SCORING_STEPS.find(s => s.key === stepKey)?.label || '')) return 'current'
  if (isLoading) {
    const currentIndex = SCORING_STEPS.findIndex(s => s.key === stepKey)
    const activeIndex = SCORING_STEPS.findIndex(s => currentStep.includes(s.key) || currentStep.includes(s.label))
    if (activeIndex >= 0 && currentIndex < activeIndex) return 'completed'
  }
  return 'pending'
}

// 安全渲染文本的辅助函数
const renderSafeText = (text: any): string => {
  if (typeof text === 'string') {
    return text
  } else if (typeof text === 'object' && text !== null) {
    // 如果是对象，尝试获取常见的文本属性
    if (text.feedback) return String(text.feedback)
    if (text.summary) return String(text.summary)
    if (text.message) return String(text.message)
    return JSON.stringify(text, null, 2)
  }
  return String(text || '')
}

export const ArticlePage: React.FC = () => {
  const [article, setArticle] = useState(sampleEssay)
  const {
    isLoading,
    error,
    currentStep,
    wordCount,
    events,
    stepResults,
    scoreResult,
    scoreEssay,
    stopStream,
    reset
  } = useReviewArticle()

  // PDF生成相关的refs
  const scoreOverviewRef = useRef<HTMLDivElement>(null)
  const radarChartRef = useRef<HTMLDivElement>(null)
  const detailAnalysisRef = useRef<HTMLDivElement>(null)
  const summaryRef = useRef<HTMLDivElement>(null)

  // 报告生成状态
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)

  const handleStart = async () => {
    try {
      await scoreEssay(article)
    } catch (err) {
      console.error('评分失败:', err)
    }
  }

  // 生成报告图片函数
  const generateReportImage = async () => {
    setIsGeneratingReport(true)
    try {
      // 创建一个包含所有评分结果的容器
      const reportContainer = document.createElement('div')
      reportContainer.style.cssText = `
        background: white;
        padding: 50px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        line-height: 1.6;
      `

      // 添加标题
      const title = document.createElement('h1')
      title.textContent = 'AI文章评分报告'
      title.style.cssText = `
        text-align: center;
        font-size: 36px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 30px;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 15px;
      `
      reportContainer.appendChild(title)

      // 添加生成时间
      const timestamp = document.createElement('p')
      timestamp.textContent = `生成时间：${new Date().toLocaleString()}`
      timestamp.style.cssText = `
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 40px;
        text-align: center;
      `
      reportContainer.appendChild(timestamp)

      // 添加作文内容部分
      const articleSection = document.createElement('div')
      articleSection.style.cssText = `
        margin-bottom: 50px;
        padding: 30px;
        background: #f8fafc;
        border-radius: 12px;
        border-left: 4px solid #3b82f6;
      `

      const articleTitle = document.createElement('h2')
      articleTitle.textContent = '📝 作文内容'
      articleTitle.style.cssText = `
        font-size: 24px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 20px;
      `
      articleSection.appendChild(articleTitle)

      const articleContent = document.createElement('div')
      articleContent.textContent = article
      articleContent.style.cssText = `
        font-size: 16px;
        color: #374151;
        line-height: 1.8;
        white-space: pre-wrap;
        background: white;
        padding: 25px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      `
      articleSection.appendChild(articleContent)
      reportContainer.appendChild(articleSection)

      // 克隆并添加各个评分区域
      const sections = [
        { ref: scoreOverviewRef, title: '📊 评分概览' },
        { ref: radarChartRef, title: '📈 五维度雷达图' },
        { ref: detailAnalysisRef, title: '📋 详细分析' },
        { ref: summaryRef, title: '📝 评分总结' }
      ]

      for (const section of sections) {
        if (section.ref.current) {
          // 创建区域标题
          const sectionTitle = document.createElement('h2')
          sectionTitle.textContent = section.title
          sectionTitle.style.cssText = `
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin: 40px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
          `
          reportContainer.appendChild(sectionTitle)

          // 克隆内容区域
          const sectionClone = section.ref.current.cloneNode(true) as HTMLElement
          sectionClone.style.cssText = `
            margin-bottom: 40px;
            page-break-inside: avoid;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          `
          reportContainer.appendChild(sectionClone)
        }
      }

      // 临时添加到页面
      reportContainer.style.position = 'absolute'
      reportContainer.style.left = '-9999px'
      reportContainer.style.top = '0'
      document.body.appendChild(reportContainer)

      // 生成图片
      const result = await snapdom(reportContainer, {
        scale: 2,
        backgroundColor: '#ffffff'
      })

      // 下载图片
      const fileName = `文章评分报告_${new Date().toISOString().slice(0, 10)}.png`
      await result.download({
        format: 'png',
        filename: fileName.replace('.png', '')
      })

      // 清理临时元素
      document.body.removeChild(reportContainer)
    } catch (error) {
      console.error('生成报告图片失败:', error)
      alert('生成报告图片失败，请稍后重试')
    } finally {
      setIsGeneratingReport(false)
    }
  }

  // 计算进度
  const completedSteps = SCORING_STEPS.filter(step => stepResults[step.key]).length
  const totalSteps = SCORING_STEPS.length
  const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">AI文章评分Demo</h1>
          <p className="text-gray-600">基于多维度分析的智能文章评分系统</p>
        </div>

        {/* 上半部分：输入和步骤进度 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 文章输入 */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>文章输入</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={article}
                onChange={(e) => setArticle(e.target.value)}
                placeholder="请输入要评分的文章..."
                className="min-h-[300px] resize-none"
                disabled={isLoading}
              />

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">
                  字数: {wordCount || article.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length}
                </span>

                <div className="flex gap-2">
                  <Button
                    onClick={handleStart}
                    disabled={isLoading || !article.trim()}
                    className="flex items-center gap-2"
                  >
                    {isLoading && <LoadingSpinner size="sm" />}
                    {isLoading ? '评分中...' : '开始评分'}
                  </Button>

                  {isLoading && (
                    <Button onClick={stopStream} variant="outline">
                      停止
                    </Button>
                  )}

                  {(events.length > 0 || scoreResult) && !isLoading && (
                    <Button onClick={reset} variant="outline">
                      重置
                    </Button>
                  )}
                </div>
              </div>

              {error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 评分进度和日志 */}
          <Card>
            <CardHeader>
              <CardTitle>评分监控</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="progress" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="progress">评分进度</TabsTrigger>
                  <TabsTrigger value="logs">过程日志</TabsTrigger>
                </TabsList>

                <TabsContent value="progress" className="space-y-4 mt-4">
                  {/* 总体进度条 */}
                  {(isLoading || completedSteps > 0) && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>总体进度</span>
                        <span>{completedSteps}/{totalSteps}</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>
                  )}

                  {/* 步骤列表 */}
                  <div className="space-y-3">
                    {SCORING_STEPS.map((step) => {
                      const status = getStepStatus(step.key, currentStep, stepResults, isLoading)
                      return (
                        <div key={step.key} className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                            status === 'completed'
                              ? 'bg-green-100 text-green-600'
                              : status === 'current'
                              ? 'bg-blue-100 text-blue-600'
                              : 'bg-gray-100 text-gray-400'
                          }`}>
                            {status === 'completed' ? '✓' : step.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className={`text-sm font-medium ${
                              status === 'completed' ? 'text-green-600' :
                              status === 'current' ? 'text-blue-600' : 'text-gray-500'
                            }`}>
                              {step.label}
                            </div>
                            <div className="text-xs text-gray-400 truncate">
                              {step.description}
                            </div>
                          </div>
                          {status === 'current' && (
                            <LoadingSpinner size="sm" className="text-blue-600" />
                          )}
                        </div>
                      )
                    })}
                  </div>

                  {/* 当前步骤提示 */}
                  {isLoading && currentStep && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <div className="text-sm font-medium text-blue-800">当前步骤</div>
                      <div className="text-sm text-blue-600">{currentStep}</div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="logs" className="mt-4">
                  <div className="space-y-2 max-h-80 overflow-y-auto">
                    {events.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        暂无日志信息
                      </div>
                    ) : (
                      events.map((event, index) => (
                        <div key={index} className="text-xs p-2 bg-gray-50 rounded border-l-2 border-blue-200">
                          <div className="font-mono text-gray-600">
                            [{new Date(event.timestamp || Date.now()).toLocaleTimeString()}]
                          </div>
                          <div className="text-gray-800">
                            {event.type === 'progress' && `🔄 ${event.message || event.step || '处理中...'}`}
                            {event.type === 'result' && `✅ ${event.step}: 完成`}
                            {event.type === 'complete' && `🎉 评分完成！`}
                            {event.type === 'error' && `❌ 错误: ${event.message}`}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* 下半部分：详细评分结果 */}
        {Object.keys(stepResults).length > 0 && (
          <div className="space-y-6">
            {/* 实时分数展示 */}
            <Card ref={scoreOverviewRef}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>评分结果</CardTitle>
                  {Object.keys(stepResults).length > 0 && (
                    <Button
                      onClick={generateReportImage}
                      variant="default"
                      size="sm"
                      className="flex items-center gap-2"
                      disabled={!scoreResult || !scoreResult.summary || isLoading || isGeneratingReport}
                    >
                      {isGeneratingReport && <LoadingSpinner size="sm" />}
                      📊 {isGeneratingReport ? '生成中...' : '生成报告图片'}
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* 总分显示 */}
                  <div className="md:col-span-2 lg:col-span-1">
                    <div className="text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                      {scoreResult ? (
                        <>
                          <div className="text-4xl font-bold text-gray-800 mb-2">
                            {scoreResult.total_score}/{scoreResult.max_score}
                          </div>
                          <Badge variant={getScoreColor(scoreResult.total_score, scoreResult.max_score)} className="text-sm">
                            {((scoreResult.total_score / scoreResult.max_score) * 100).toFixed(1)}%
                          </Badge>
                        </>
                      ) : (
                        <>
                          <div className="text-2xl font-bold text-gray-600 mb-2">
                            评分中...
                          </div>
                          <div className="text-sm text-gray-500">
                            {completedSteps}/{totalSteps} 步骤完成
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* 各维度得分 - 实时显示 */}
                  <div className="md:col-span-2 lg:col-span-2">
                    <div className="grid grid-cols-3 gap-3">
                      <div className="p-3 bg-white rounded-lg border">
                        <div className="text-sm text-gray-500 mb-1">字数标点</div>
                        <div className="text-xl font-semibold text-indigo-600">
                          {stepResults.word_count?.score || scoreResult?.word_score || '-'}/5
                        </div>
                      </div>
                      <div className="p-3 bg-white rounded-lg border">
                        <div className="text-sm text-gray-500 mb-1">主题立意</div>
                        <div className="text-xl font-semibold text-blue-600">
                          {stepResults.topic_analysis?.score || scoreResult?.topic_score || '-'}/15
                        </div>
                      </div>
                      <div className="p-3 bg-white rounded-lg border">
                        <div className="text-sm text-gray-500 mb-1">结构层次</div>
                        <div className="text-xl font-semibold text-green-600">
                          {stepResults.structure_analysis?.score || scoreResult?.structure_score || '-'}/10
                        </div>
                      </div>
                      <div className="p-3 bg-white rounded-lg border">
                        <div className="text-sm text-gray-500 mb-1">内容表达</div>
                        <div className="text-xl font-semibold text-purple-600">
                          {stepResults.content_analysis?.score || scoreResult?.content_score || '-'}/15
                        </div>
                      </div>
                      <div className="p-3 bg-white rounded-lg border">
                        <div className="text-sm text-gray-500 mb-1">语言文字</div>
                        <div className="text-xl font-semibold text-orange-600">
                          {stepResults.language_analysis?.score || scoreResult?.language_score || '-'}/15
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 雷达图 - 显示5个维度 */}
            {(scoreResult ||
              ['word_count', 'topic_analysis', 'structure_analysis', 'content_analysis', 'language_analysis']
                .filter(step => stepResults[step]?.score).length >= 3) && (
              <Card ref={radarChartRef}>
                <CardHeader>
                  <CardTitle>📊 五维度雷达图</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScoreRadarChart
                    data={{
                      word_score: stepResults.word_count?.score || scoreResult?.word_score || 0,
                      topic_score: stepResults.topic_analysis?.score || scoreResult?.topic_score || 0,
                      structure_score: stepResults.structure_analysis?.score || scoreResult?.structure_score || 0,
                      content_score: stepResults.content_analysis?.score || scoreResult?.content_score || 0,
                      language_score: stepResults.language_analysis?.score || scoreResult?.language_score || 0
                    }}
                  />
                </CardContent>
              </Card>
            )}

            {/* 详细分析结果 */}
            <div ref={detailAnalysisRef} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 主题立意分析 */}
              {stepResults.topic_analysis && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      🎯 主题立意分析
                      {stepResults.topic_analysis.score && (
                        <Badge variant="outline">{stepResults.topic_analysis.score}/15</Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* 显示详细分析内容 */}
                      {stepResults.topic_analysis.details?.feedback && (
                        <MarkdownRenderer
                          content={renderSafeText(stepResults.topic_analysis.details.feedback)}
                          className="text-gray-700"
                        />
                      )}
                      {stepResults.topic_analysis.details && (
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {stepResults.topic_analysis.details.theme_clarity_score !== undefined && (
                            <div>主题明确性: {stepResults.topic_analysis.details.theme_clarity_score}</div>
                          )}
                          {stepResults.topic_analysis.details.depth_score !== undefined && (
                            <div>深度分析: {stepResults.topic_analysis.details.depth_score}</div>
                          )}
                          {stepResults.topic_analysis.details.relevance_score !== undefined && (
                            <div>相关性: {stepResults.topic_analysis.details.relevance_score}</div>
                          )}
                          {stepResults.topic_analysis.details.values_score !== undefined && (
                            <div>价值观: {stepResults.topic_analysis.details.values_score}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 结构层次分析 */}
              {stepResults.structure_analysis && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      🏗️ 结构层次分析
                      {stepResults.structure_analysis.score && (
                        <Badge variant="outline">{stepResults.structure_analysis.score}/10</Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stepResults.structure_analysis.details?.feedback && (
                        <MarkdownRenderer
                          content={renderSafeText(stepResults.structure_analysis.details.feedback)}
                          className="text-gray-700"
                        />
                      )}
                      {stepResults.structure_analysis.details && (
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {stepResults.structure_analysis.details.opening_score !== undefined && (
                            <div>开头: {stepResults.structure_analysis.details.opening_score}</div>
                          )}
                          {stepResults.structure_analysis.details.paragraph_score !== undefined && (
                            <div>段落: {stepResults.structure_analysis.details.paragraph_score}</div>
                          )}
                          {stepResults.structure_analysis.details.transition_score !== undefined && (
                            <div>过渡: {stepResults.structure_analysis.details.transition_score}</div>
                          )}
                          {stepResults.structure_analysis.details.ending_score !== undefined && (
                            <div>结尾: {stepResults.structure_analysis.details.ending_score}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 内容表达分析 */}
              {stepResults.content_analysis && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      📝 内容表达分析
                      {stepResults.content_analysis.score && (
                        <Badge variant="outline">{stepResults.content_analysis.score}/15</Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stepResults.content_analysis.details?.feedback && (
                        <MarkdownRenderer
                          content={renderSafeText(stepResults.content_analysis.details.feedback)}
                          className="text-gray-700"
                        />
                      )}
                      {stepResults.content_analysis.details && (
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {stepResults.content_analysis.details.material_score !== undefined && (
                            <div>素材运用: {stepResults.content_analysis.details.material_score}</div>
                          )}
                          {stepResults.content_analysis.details.logic_score !== undefined && (
                            <div>逻辑性: {stepResults.content_analysis.details.logic_score}</div>
                          )}
                          {stepResults.content_analysis.details.innovation_score !== undefined && (
                            <div>创新性: {stepResults.content_analysis.details.innovation_score}</div>
                          )}
                          {stepResults.content_analysis.details.depth_score !== undefined && (
                            <div>深度: {stepResults.content_analysis.details.depth_score}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 语言文字分析 */}
              {stepResults.language_analysis && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      ✍️ 语言文字分析
                      {stepResults.language_analysis.score && (
                        <Badge variant="outline">{stepResults.language_analysis.score}/15</Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {stepResults.language_analysis.details?.feedback && (
                        <MarkdownRenderer
                          content={renderSafeText(stepResults.language_analysis.details.feedback)}
                          className="text-gray-700"
                        />
                      )}
                      {stepResults.language_analysis.details && (
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {stepResults.language_analysis.details.accuracy_score !== undefined && (
                            <div>准确性: {stepResults.language_analysis.details.accuracy_score}</div>
                          )}
                          {stepResults.language_analysis.details.fluency_score !== undefined && (
                            <div>流畅性: {stepResults.language_analysis.details.fluency_score}</div>
                          )}
                          {stepResults.language_analysis.details.vividness_score !== undefined && (
                            <div>生动性: {stepResults.language_analysis.details.vividness_score}</div>
                          )}
                          {stepResults.language_analysis.details.standard_score !== undefined && (
                            <div>规范性: {stepResults.language_analysis.details.standard_score}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* 评分总结 */}
            {scoreResult?.summary && (
              <Card ref={summaryRef}>
                <CardHeader>
                  <CardTitle>📋 评分总结</CardTitle>
                </CardHeader>
                <CardContent>
                  <MarkdownRenderer
                    content={renderSafeText(scoreResult.summary)}
                    className="text-gray-700"
                  />
                </CardContent>
              </Card>
            )}
          </div>
        )}


      </div>
    </div>
  )
}
