# PDF报告生成功能

## 功能概述

为 AI文章评分Demo 添加了生成报告图片的功能，使用 snapDOM 库将评分结果转换为高质量的PNG图片。

## 功能特点

- 📊 **一键生成报告图片**：将完整的评分结果生成为PNG格式的报告图片
- 🎨 **美观的报告布局**：包含标题、文章信息、生成时间和所有评分内容
- 🔒 **智能按钮状态**：只有在所有评分数据生成完成后才能点击生成按钮
- 📱 **响应式设计**：报告图片适配不同屏幕尺寸
- ⚡ **高性能**：使用 snapDOM 库，生成速度快，图片质量高

## 使用方法

1. **输入文章**：在文章输入框中输入要评分的文章内容
2. **开始评分**：点击"开始评分"按钮，等待AI完成评分
3. **生成报告**：评分完成后，在"评分结果"标题右侧会出现"📊 生成报告图片"按钮
4. **下载图片**：点击按钮即可自动下载包含完整评分结果的PNG图片

## 报告内容

生成的报告图片包含以下内容：

- **报告标题**：AI文章评分报告
- **文章信息**：文章标题（前100个字符）
- **生成时间**：报告生成的具体时间
- **评分概览**：总分和各维度得分
- **五维度雷达图**：可视化展示各维度表现
- **详细分析**：各维度的详细分析结果
- **评分总结**：AI生成的综合评价

## 技术实现

- **snapDOM**：用于将DOM元素转换为高质量图片
- **React Refs**：引用需要捕获的DOM元素
- **动态布局**：创建临时容器组合所有评分内容
- **自动下载**：生成后自动触发浏览器下载

## 按钮状态

- **置灰状态**：评分进行中或数据不完整时
- **可点击状态**：所有评分数据生成完成且有评分总结时
- **位置**：评分结果卡片标题右侧

## 文件命名

生成的报告图片文件名格式：`文章评分报告_YYYY-MM-DD.png`

例如：`文章评分报告_2024-01-15.png`
